const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const path = require('path');

// Load environment variables from server/.env
require('dotenv').config({ path: path.join(__dirname, '.env') });

// Fix Mongoose deprecation warnings
mongoose.set('strictQuery', false);

// Import routes
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const deviceRoutes = require('./routes/devices');
const userDeviceRoutes = require('./routes/userDevices');
const locationRoutes = require('./routes/locations');
const qrRoutes = require('./routes/qr');
const gpsRoutes = require('./routes/gpsRoutes');
const emailRoutes = require('./routes/email');
const scanHistoryRoutes = require('./routes/scanHistory');
const locationHistoryRoutes = require('./routes/locationHistory');

// Import middleware
const { errorHandler } = require('./middleware/errorHandler');

const app = express();

// Security middleware
app.use(helmet());

// Rate limiting (disabled for development)
if (process.env.NODE_ENV === 'production') {
  const limiter = rateLimit({
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
    max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP, please try again later.'
  });
  app.use(limiter);
}

// CORS configuration
app.use(cors({
  origin: [
    'http://localhost:3000',
    'http://localhost:3001',
    process.env.CLIENT_URL
  ].filter(Boolean),
  credentials: true
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Database connection
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/gpstracker', {
})
.then(() => {
  console.log('✅ Connected to MongoDB');
  
  // Create default admin and super admin users if none exist
  createDefaultUsers();
})
.catch((error) => {
  console.error('❌ MongoDB connection error:', error);
  process.exit(1);
});

// Create default admin and super admin users
const createDefaultUsers = async () => {
  try {
    const User = require('./models/User');
    const bcrypt = require('bcryptjs');
    
    // Create default admin user
    const adminExists = await User.findOne({ role: 'admin' });
    if (!adminExists) {
      const hashedAdminPassword = await bcrypt.hash('Admin@123', 12);
      const defaultAdmin = new User({
        username: 'admin',
        email: '<EMAIL>',
        password: hashedAdminPassword,
        role: 'admin',
        firstName: 'Admin',
        lastName: 'User',
        company: 'Addwise Tracker',
        phone: '**********',
        isVerified: true
      });
      await defaultAdmin.save();
      console.log('✅ Default admin user created');
    }
    
    // Create super admin user
    const superAdminExists = await User.findOne({ username: 'superadmin' });
    if (!superAdminExists) {
      const hashedSuperAdminPassword = await bcrypt.hash('SuperAdmin@123', 12);
      const superAdmin = new User({
        username: 'superadmin',
        email: '<EMAIL>',
        password: hashedSuperAdminPassword,
        role: 'superadmin',
        firstName: 'Super',
        lastName: 'Admin',
        company: 'Addwise Tracker',
        phone: '**********',
        isVerified: true
      });
      await superAdmin.save();
      console.log('✅ Super admin user created');
    }
  } catch (error) {
    console.error('❌ Error creating default users:', error);
  }
};

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'GPS Tracker Backend is running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/devices', deviceRoutes);
app.use('/api/devices', userDeviceRoutes);
app.use('/api/locations', locationRoutes);
app.use('/api/qr', qrRoutes);
app.use('/api/gps', gpsRoutes);
app.use('/api/email', emailRoutes);
app.use('/api/scan-history', scanHistoryRoutes);
app.use('/api/location-history', locationHistoryRoutes);

// Serve static assets if in production
if (process.env.NODE_ENV === 'production') {
  app.use(express.static('client/build'));

  app.get('*', (req, res) => {
    res.sendFile(path.resolve(__dirname, '..', 'client', 'build', 'index.html'));
  });
}

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found'
  });
});

// Error handling middleware
app.use(errorHandler);

// Start server
const PORT = process.env.PORT || 5001;
app.listen(PORT, () => {
  console.log(`🚀 GPS Tracker Server running on port ${PORT}`);
  console.log(`📍 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🌐 Client URL: ${process.env.CLIENT_URL || 'http://localhost:3000'}`);
  console.log(`🗺️ GPS API available at: http://localhost:${PORT}/api/gps/health`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received. Shutting down gracefully...');
  mongoose.connection.close(() => {
    console.log('MongoDB connection closed.');
    process.exit(0);
  });
});

module.exports = app;